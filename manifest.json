{
    "name" : "xzb-yhd",
    "appid" : "__UNI__FAC18C5",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "modules" : {
            "Maps" : {},
            "Geolocation" : {},
            "Payment" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_CHECKIN_PROPERTIES\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_SURFACE_FLINGER\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCOUNT_MANAGER\"/>",
                    "<uses-permission android:name=\"android.permission.ADD_VOICEMAIL\"/>",
                    "<uses-permission android:name=\"android.permission.AUTHENTICATE_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.BATTERY_STATS\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_ACCESSIBILITY_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_APPWIDGET\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_CARRIER_MESSAGING_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_DEVICE_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_DREAM_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_INPUT_METHOD\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_NFC_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_NOTIFICATION_LISTENER_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_PRINT_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_REMOTEVIEWS\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_TEXT_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_TV_INPUT\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_VOICE_INTERACTION\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_VPN_SERVICE\"/>",
                    "<uses-permission android:name=\"android.permission.BIND_WALLPAPER\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_PRIVILEGED\"/>",
                    "<uses-permission android:name=\"android.permission.BODY_SENSORS\"/>",
                    "<uses-permission android:name=\"android.permission.BRICK\"/>",
                    "<uses-permission android:name=\"android.permission.BROADCAST_PACKAGE_REMOVED\"/>",
                    "<uses-permission android:name=\"android.permission.BROADCAST_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.BROADCAST_STICKY\"/>",
                    "<uses-permission android:name=\"android.permission.BROADCAST_WAP_PUSH\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PRIVILEGED\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CAPTURE_AUDIO_OUTPUT\"/>",
                    "<uses-permission android:name=\"android.permission.CAPTURE_SECURE_VIDEO_OUTPUT\"/>",
                    "<uses-permission android:name=\"android.permission.CAPTURE_VIDEO_OUTPUT\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_COMPONENT_ENABLED_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_CONFIGURATION\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_MULTICAST_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CLEAR_APP_CACHE\"/>",
                    "<uses-permission android:name=\"android.permission.CLEAR_APP_USER_DATA\"/>",
                    "<uses-permission android:name=\"android.permission.CONTROL_LOCATION_UPDATES\"/>",
                    "<uses-permission android:name=\"android.permission.DELETE_CACHE_FILES\"/>",
                    "<uses-permission android:name=\"android.permission.DELETE_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.DEVICE_POWER\"/>",
                    "<uses-permission android:name=\"android.permission.DIAGNOSTIC\"/>",
                    "<uses-permission android:name=\"android.permission.DISABLE_KEYGUARD\"/>",
                    "<uses-permission android:name=\"android.permission.DUMP\"/>",
                    "<uses-permission android:name=\"android.permission.EXPAND_STATUS_BAR\"/>",
                    "<uses-permission android:name=\"android.permission.FACTORY_TEST\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.FORCE_BACK\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.GET_PACKAGE_SIZE\"/>",
                    "<uses-permission android:name=\"android.permission.GET_TASKS\"/>",
                    "<uses-permission android:name=\"android.permission.GET_TOP_ACTIVITY_INFO\"/>",
                    "<uses-permission android:name=\"android.permission.GLOBAL_SEARCH\"/>",
                    "<uses-permission android:name=\"android.permission.HARDWARE_TEST\"/>",
                    "<uses-permission android:name=\"android.permission.INJECT_EVENTS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_LOCATION_PROVIDER\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_SHORTCUT\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNAL_SYSTEM_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.KILL_BACKGROUND_PROCESSES\"/>",
                    "<uses-permission android:name=\"android.permission.LOCATION_HARDWARE\"/>",
                    "<uses-permission android:name=\"android.permission.MANAGE_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MANAGE_APP_TOKENS\"/>",
                    "<uses-permission android:name=\"android.permission.MANAGE_DOCUMENTS\"/>",
                    "<uses-permission android:name=\"android.permission.MASTER_CLEAR\"/>",
                    "<uses-permission android:name=\"android.permission.MEDIA_CONTENT_CONTROL\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_FORMAT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.NFC\"/>",
                    "<uses-permission android:name=\"android.permission.PERSISTENT_ACTIVITY\"/>",
                    "<uses-permission android:name=\"android.permission.PROCESS_OUTGOING_CALLS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CALENDAR\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CALL_LOG\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_FRAME_BUFFER\"/>",
                    "<uses-permission android:name=\"android.permission.READ_HISTORY_BOOKMARKS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_INPUT_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PROFILE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SOCIAL_STREAM\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SYNC_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SYNC_STATS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_USER_DICTIONARY\"/>",
                    "<uses-permission android:name=\"android.permission.READ_VOICEMAIL\"/>",
                    "<uses-permission android:name=\"android.permission.REBOOT\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_MMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_WAP_PUSH\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REORDER_TASKS\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.RESTART_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_RESPOND_VIA_MESSAGE\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.SET_ACTIVITY_WATCHER\"/>",
                    "<uses-permission android:name=\"android.permission.SET_ALARM\"/>",
                    "<uses-permission android:name=\"android.permission.SET_ALWAYS_FINISH\"/>",
                    "<uses-permission android:name=\"android.permission.SET_ANIMATION_SCALE\"/>",
                    "<uses-permission android:name=\"android.permission.SET_DEBUG_APP\"/>",
                    "<uses-permission android:name=\"android.permission.SET_ORIENTATION\"/>",
                    "<uses-permission android:name=\"android.permission.SET_POINTER_SPEED\"/>",
                    "<uses-permission android:name=\"android.permission.SET_PREFERRED_APPLICATIONS\"/>",
                    "<uses-permission android:name=\"android.permission.SET_PROCESS_LIMIT\"/>",
                    "<uses-permission android:name=\"android.permission.SET_TIME\"/>",
                    "<uses-permission android:name=\"android.permission.SET_TIME_ZONE\"/>",
                    "<uses-permission android:name=\"android.permission.SET_WALLPAPER\"/>",
                    "<uses-permission android:name=\"android.permission.SET_WALLPAPER_HINTS\"/>",
                    "<uses-permission android:name=\"android.permission.SIGNAL_PERSISTENT_PROCESSES\"/>",
                    "<uses-permission android:name=\"android.permission.STATUS_BAR\"/>",
                    "<uses-permission android:name=\"android.permission.SUBSCRIBED_FEEDS_READ\"/>",
                    "<uses-permission android:name=\"android.permission.SUBSCRIBED_FEEDS_WRITE\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.TRANSMIT_IR\"/>",
                    "<uses-permission android:name=\"android.permission.UNINSTALL_SHORTCUT\"/>",
                    "<uses-permission android:name=\"android.permission.UPDATE_DEVICE_STATS\"/>",
                    "<uses-permission android:name=\"android.permission.USE_CREDENTIALS\"/>",
                    "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\"/>",
                    "<uses-permission android:name=\"android.permission.USE_SIP\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_APN_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CALENDAR\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CALL_LOG\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_GSERVICES\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_HISTORY_BOOKMARKS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_PROFILE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SECURE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SOCIAL_STREAM\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SYNC_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_USER_DICTIONARY\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_VOICEMAIL\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "x86" ],
                "autoSdkPermissions" : true
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "",
                        "appkey_android" : "284498fab56133330077ceb92ff5b7d4"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "",
                        "appkey_android" : "284498fab56133330077ceb92ff5b7d4"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx8634b2ce0fd609b2",
                        "UniversalLinks" : ""
                    }
                }
            }
        },
        "splashscreen" : {
            "waiting" : true,
            "autoclose" : true
        },
        "background" : "red", //背景色
        "safearea" : {
            //安全区域配置，仅iOS平台生效  
            "bottom" : {
                // 底部安全区域配置  
                "offset" : "none" // 底部安全区域偏移，"none"表示不空出安全区域，"auto"自动计算空出安全区域，默认值为"none"  
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "appid" : "wxc5cf728c76287180",
        "setting" : {
            "urlCheck" : false,
            "minified" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "获取您的位置信息"
            }
        }
    },
    "vueVersion" : "3",
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "6FFBZ-JMPW2-DJVU2-CEO6V-FCJF6-AJFNX"
                }
            }
        }
    }
}
