{
  "pages": [
    // 首页
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "云岚到家",
        "enablePullDownRefresh": true,
        "backgroundTextStyle": "dark"
      }
    },
    // 手机登录页
    {
      "path": "pages/login/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "登录",
        "enablePullDownRefresh": false
      }
    },
    // 我的
    {
      "path": "pages/my/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "我的"
      }
    },
    // 全部服务
    {
      "path": "pages/service/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "全部服务"
      }
    },
    // 消息
    {
      "path": "pages/message/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "消息"
      }
    },
    // 城市
    {
      "path": "pages/city/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "城市"
      }
    },
    {
      "path": "pages/service/components/airMaintenance",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "服务详情"
      }
    },
    //我的评价
    {
      "path": "pages/commit/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "我的评价"
      }
    },
    // 地址栏
    {
      "path": "pages/address/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "地址管理"
      }
    },
    // 支付订单
    {
      "path": "pages/pay/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "支付订单"
      }
    },
    // 下单成功
    {
      "path": "pages/pay/components/paySuccessful",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "下单成功"
      }
    },
    // 搜索
    {
      "path": "pages/search/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "搜索"
      }
    },
    {
      "path": "pages/coupon/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "抢单"
      }
    },
    {
      "path": "pages/coupon/coupon",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "优惠券"
      }
    },
    {
      "path": "components/Foot/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }
    }
  ],
  "subPackages": [{
		"root": "subPages", //子包的根目录
		"pages": [
      {
        "path": "order/index",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "订单列表"
        }
      },
      {
        "path": "order/details",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "订单详情"
        }
      },
      {
        "path": "order/cancelRule",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "规则说明"
        }
      },
      {
        "path": "order/cancel",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "取消订单"
        }
      },
      // 评价
      {
        "path": "order/components/evaluate",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": "评价"
        }
      },
      // 编辑地址
    {
      "path": "address-info/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "编辑地址"
      }
    },
    {
      "path": "history/index",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "历史订单"
      }
    },
    {
      "path": "history/details",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "历史订单详情"
      }
    }
		]
  }],
    "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "云岚到家",
    // "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "onReachBottomDistance": 50,
    "enablePullDownRefresh": true
    // "app-plus": {
    // 	"background": "#efeff4"
    // }
  },
  "condition": {
    // 模式配置，仅开发期间生效
    "current": 0, // 当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", // 模式名称
        "path": "", // 启动页面，必选
        "query": "" // 启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
  // "tabBar": {
  // 	"borderStyle":"black",
  // 	"color": "#000000",
  // 	"selectedColor": "#E94739",
  // 	"list": [{
  // 			"pagePath": "pages/index/index",
  // 			"text": "首页",
  // 			"iconPath": "static/sy.png",
  // 			"selectedIconPath": "static/sy1.png"
  // 		},
  // 		{
  // 			"pagePath": "pages/service/index",
  // 			"text": "全部服务",
  // 			"iconPath": "static/fw.png",
  // 			"selectedIconPath": "static/qb1.png"
  // 		},{
  // 			"pagePath": "pages/message/index",
  // 			"text": "消息",
  // 			"iconPath": "static/x x.png",
  // 			"selectedIconPath": "static/xx1.png"
  // 		},
  // 		{
  // 			"pagePath": "pages/my/index",
  // 			"text": "我的",
  // 			"iconPath": "static/wd.png",
  // 			"selectedIconPath": "static/wd2.png"
  // 		}
  // 	]
  // }
}
