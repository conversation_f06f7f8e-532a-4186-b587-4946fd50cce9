@import url('@/styles/theme.scss');
body,
uni-page-body,
uni-page-head,
.uni-page-head {
	// background-color: var(--neutral-color-white) !important;
}
uni-app {
  min-width: 750rpx;
  max-width: 1500rpx;
  margin: 0 auto;
}
button {
  margin: 0;
}
body {
  color: var(--neutral-color-main);
  font-size: var(--font-size-14);
  background-color: #F8F8F8 !important;
}
view{
  color: var(--neutral-color-main);
}
.font-col {
	color: var(--essential-color-red);
}
.font-grey{
  color: var(--neutral-color-font);
}
// 白色
.bg-wt {
  background-color: #fff;
}
// 按钮
.btn {
  border: 2rpx solid var(--essential-color-red);
  border-radius: 45.1rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
}
.s-btn {
  height: 56rpx;
  background: #ffffff;
  border: 2rpx solid #dddddd;
  border-radius: 28rpx;
}
.cancel-btn {
  background: var(--neutral-color-white);
  color: var(--essential-color-red);
}
.agree-btn {
  background: var(--essential-color-red);
  color: var(--neutral-color-white);
  border: none;
}
.disabled-btn {
  background: #CCCCCC;
  color: #ffffff;
  border: none;
  // 不可点击
  pointer-events: none;
}
.load-ft {
  font-size: 36rpx;
  letter-spacing: 0.42rpx;
  color: var(--neutral-color-font);
  font-weight: 400;
  height: 50rpx;
  line-height: 50rpx;
}
// 输入框
:deep(.uni-easyinput) {
  .uni-easyinput__content {
    background-color: var(--neutral-color-background);
  }
}
// 吸底页面底部按钮
.pageFixFoot {
  background-color: #fff;
  padding-bottom: 59rpx;
  padding-top: 17rpx;
  width: 100%;
  z-index: 1000;
  // 位于底部
  position: fixed;
  bottom: 0;
  .btn {
    // 居中
    margin: 0 auto;
    width: 400rpx;
    height: 88rpx;
  }
}
// 页面底部按钮
.pageFoot {
  background-color: #fff;
  padding-bottom: 59rpx;
  padding-top: 17rpx;
  margin-top: 21rpx;
  width: 100%;
  .btn {
    // 居中
    margin: 0 auto;
    width: 400rpx;
    height: 88rpx;
  }
}
.nextIcon {
  background: url(@/static/<EMAIL>) no-repeat;
  background-size: contain;
  width: 20rpx;
  height: 20rpx;
}
// 定位图标
.locationIcon {
  background: url(@/static/location.png) no-repeat;
  background-size: contain;
  width: 32rpx;
  height: 32rpx;
  margin-right: 3rpx;
}
.iconOpacity{
  opacity: 0.5;
  margin-left: 13rpx;
}
// 弹层
.uniPopupBox {
	.popup-content {
		background-color: var(--neutral-color-white);
		border-radius: 32rpx;
		text-align: center;
		width: 600rpx;
		// margin: 0 64rpx;
		.popupCon {
			padding: 40rpx 60rpx 44rpx;
			.tip {
				font-size: var(--font-size-16);
				line-height: 44rpx;
				padding: 24rpx 0 20rpx;
				color: var(--neutral-color-main);
				font-weight: 600;
			}
			.text {
				font-size: var(--font-size-13);
				line-height: 36rpx;
				color: var(--neutral-color-placeholder);
				text {
					display: block;
				}
			}
			.operateText {
				padding-bottom: 0;
			}
			.popIcon {
				display: flex;
				justify-content: center;
				align-items: center;
				icon {
					// background: url(@/static/warning.png) no-repeat;
					background-size: contain;
					width: 80rpx;
					height: 80rpx;
					&.pdfIcon {
						// background: url(@/static/pdf.png) no-repeat;
						background-size: contain;
						width: 120rpx;
						height: 120rpx;
					}
				}
			}
		}
	}
	.popupFoot {
		border-top: 1rpx solid var(--neutral-color-line);
		height: 96rpx;
		line-height: 96rpx;
		color: var(--essential-color-red);
		font-size: var(--font-size-16);
		// font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		text {
			flex: 1;
			&:first-child {
				color: var(--neutral-color-main);
			}
			&:last-child {
				border-left: 1px solid var(--neutral-color-line);
			}
		}
	}
}
// 底部导航
.footBox {
	position: fixed;
	left: 0;
	bottom: 0;
	right: 0;
	background: var(--neutral-color-white);
	box-shadow: 0 4rpx 16rpx 0 rgba(162, 162, 162, 0.25);
	padding: 20rpx 0 40rpx;
	display: flex;
	align-items: center;
	text-align: center;
	justify-content: center;
	width: 100%;
	.uni-tabbar {
		width: 100%;
		display: flex;
	}
	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: var(--neutral-color-main);
		.item-img {
			width: 44rpx;
			height: 44rpx;
			display: block;
		}
		.item-name {
			text-align: center;
			font-size: 20rpx;
			line-height: 28rpx;
		}
		.uni-tabbar__label {
			font-size: 20rpx;
			line-height: 32rpx;
		}
		&.active {
			.uni-tabbar__label {
				color: var(--essential-color-red);
			}
		}
	}
}
.appBox {
	.box {
		margin: 20rpx 20rpx 0;
	}
}
.orderPay {
	text-align: center;
	font-size: var(--font-size-13);
	line-height: 36rpx;
	padding: 100rpx 0 68rpx;
	.time {
		color: var(--neutral-color-placeholder);
	}
	.money {
		color: var(--neutral-color-black);
		padding-right: 12rpx;
		line-height: 84rpx;
		font-weight: 700;
		font-size: 40rpx;
		text {
			font-size: 80rpx;
		}
	}
}
.boxRadius {
	border-radius: 8rpx;
}
.uni-list {
	flex: 1;
}
.wechatIcon {
  background: url(@/static/wechat.png) no-repeat;
  background-size: contain;
  display: inline-block;
  width: 48rpx;
  height: 48rpx;
  vertical-align: middle;
  margin-right: 20rpx;
}
.uni-list-item {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex: 1;
	flex-direction: row;
	background-color: #ffffff;
	
}

.uni-list-item__container {
	padding: 16rpx 0 16rpx 0;
	width: 100%;
	flex: 1;
	position: relative;
	/* #ifndef APP-NVUE */
	display: flex;
	box-sizing: border-box;
	/* #endif */
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
}
.uni-list-item .uni-icon-wrapper {
	padding-right: 0 !important;
}
.uni-list-item__content-title {
	font-size: 14px;
}
.orderInfo {
	padding-top: 16rpx;
	.orderList {
		border-bottom: 1px solid var(--neutral-color-line);
		margin-top: 16rpx;
		padding-bottom: 16rpx;
		.item {
			display: flex;
			line-height: 40rpx;
			padding: 16rpx 0;
			text {
				color: var(--neutral-color-placeholder);
				display: inline-block;
				width: 170rpx;
				padding-right: 30rpx;
			}
      view{
        font-size: var(--font-size-14);
      }
      .two{
        text{
          font-size: var(--font-size-14);
          color: var(--neutral-color-main);
          margin-right: 35rpx;
          display: inline-block;
          padding-right: 0;
          width: auto;
        }
      }
		}
		&:last-child {
			border: 0 none;
		}
	}
}
.itemTab {
	padding: 0 !important;
  background-color: #fff;
	.tabBox {
		display: flex;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 20rpx;
    justify-content: space-between;
		.tabItem {
			// flex: 1;
			margin: 0 20rpx;
			min-width: 100rpx;
			text-align: center;
			position: relative;
			&.active {
				color: var(--essential-color-red);

				.bLine {
					height: 6rpx;
					background: var(--essential-color-red);
					border-radius: 3rpx;
					position: absolute;
					width: 60rpx;
					top: 74rpx;
					display: inline-block;
					z-index: 9;
					left: 50%;
					transform: translateX(-50%);
				}
			}
		}
	}
}
.itemListBox {
	:deep(.timeList) {
		.item {
			padding: 35rpx 32rpx 15rpx 28rpx;
		}
		.itemCon {
			image {
				width: 150rpx;
				height: 150rpx;
			}
			.rText {
				.info {
					padding-bottom: 8rpx;
				}
				.tit {
					padding-bottom: 15rpx;
					margin-top: -8rpx;
				}
			}
		}
	}
}
// 列表
.appList {
	.item {
		padding: 30rpx;
		line-height: 40rpx;
		margin: 32rpx 26rpx 0;
    &:last-child{
      margin-bottom: 32rpx;
    }
		.title {
			display: flex;
			align-items: center;
			.tag {
				display: inline-block;
				height: 48rpx;
				line-height: 48rpx;

				border-radius: 32rpx;
				padding: 0 12rpx;
				margin-left: 26rpx;
			}
			.tagApp {
				background: #fffbef;
				color: var(--essential-color-red);
			}
			.tagLook {
				background: #f8fdff;
				color: #1686fe;
			}
			.mobile {
				flex: 1;
				text-align: right;
			}
		}
		.time {
			font-size: 48rpx;
			line-height: 66rpx;
			padding: 16rpx 0 20rpx;
			display: flex;
			.status {
				font-size: var(--font-size-13);
				margin-left: 20rpx;
			}
		}
		.info {
			display: flex;
			align-items: center;
			line-height: 40rpx;
			position: relative;
			.line {
				display: inline-block;
				width: 1rpx;
				height: 24rpx;
				background: var(--neutral-color-main);
				margin: 0 20rpx;
			}
			.mobile {
				flex: 1;
				text-align: right;
			}
		}
		.footBtn {
			margin: 0;
			position: absolute;
			right: 0rpx;
			bottom: 0rpx;
			.btSmall {
				width: 144rpx;
				display: inline-block;
			}
		}
	}
}
.appList {
	:deep(.itemCon) {
		display: flex;
		padding: 0 12rpx 20rpx;
		.rText {
			padding-left: 30rpx;
		}
		.tit {
			font-size: var(--font-size-16);
			font-family: 500;
			line-height: 44rpx;
			padding-bottom: 15rpx;
			color: var(--neutral-color-main);
			display: flex;
			align-items: center;
			.tag {
				height: 36rpx;
				line-height: 34rpx;
				background: #ffece7;
				border-radius: 32px;
				padding: 0 12rpx;
				display: inline-block;
				font-size: var(--font-size-11);
				color: var(--essential-color-red);
				margin-left: 12rpx;
			}
		}
		.info {
			font-size: var(--font-size-14);
			color: var(--neutral-color-placeholder);
			line-height: 34rpx;
		}
		.icon {
			height: 36rpx;
			line-height: 36rpx;
		}
		image {
			width: 100rpx;
			height: 100rpx;
			border-radius: 16rpx;
		}
	}
	.itemTit {
		display: flex;
		padding: 0 12rpx 28rpx;
		line-height: 34rpx;
		text {
			&:first-child {
				flex: 1;
				font-size: var(--font-size-12);
				color: var(--neutral-color-placeholder);
			}
			&:nth-child(2) {
				font-size: var(--font-size-14);
			}
		}
	}
	.foot {
		border-top: 1px solid var(--neutral-color-line);
		padding: 24rpx 12rpx 14rpx;
		display: flex;
		justify-content: space-between;
		line-height: 56rpx;
		font-size: var(--font-size-12);
		color: var(--neutral-color-placeholder);
    .amount{
      font-size: var(--font-size-14);
    }
		text {
			flex: 1;
		}
		label {
			display: inline-block;
			height: 56rpx;
			line-height: 54rpx;
			color: var(--neutral-color-main);
			font-size: var(--font-size-12);
			padding: 0;
			margin-left: 20rpx;
			&.bt-orange {
				color: var(--essential-color-red);
			}
		}
	}
}
.box {
  background-color: var(--neutral-color-white);
  padding: 0 28rpx;
}
.operateTip {
	background-image: linear-gradient(242deg, #FE875A 3%, #F74145 100%);;
	padding: 40rpx;
	line-height: 40rpx;
	color: var(--neutral-color-white);
	font-size: var(--font-size-14);
	height: 100rpx;
	display: flex;
	align-items: center;
	.tit {
		font-size: 36rpx;
		line-height: 50rpx;
	}
	&:last-child {
		padding-top: 8rpx;
	}
}
.projectBox {
	padding: 30rpx 28rpx 20rpx;
	.foot {
		text-align: right;
		padding: 20rpx 0 0;
		border-top: 1px solid var(--neutral-color-line);
		.font-col {
			font-size: var(--font-size-14);
			padding-left: 12rpx;
		}
	}
}
.projectInfo {
	display: flex;
	padding-bottom: 20rpx;
	.head {
		image {
			border-radius: 20rpx;
			width: 140rpx;
			height: 124rpx;
		}
	}
	.rText {
		padding-left: 30rpx;
		flex: 1;
		line-height: 44rpx;
		font-size: var(--font-size-16);
		.tit {
			padding-bottom: 14rpx;
		}
		.projectFee {
			.hour {
				font-size: var(--font-size-11);
				color: var(--neutral-color-placeholder);
			}
		}
	}
}
.radioList {
	background: var(--neutral-color-white);
	.uni-list-cell {
		width: calc(100% - 30px);
		display: flex;
		padding: 40rpx 20rpx 40rpx 40rpx;

		color: var(--neutral-color-main);
		border-top: 1px solid var(--neutral-color-line);
		view {
			line-height: 40rpx;
			&:first-child {
				flex: 1;
			}
		}
		radio {
			transform: scale(0.7);
		}
	}
}