@import url('./theme.scss');
body,
uni-page-body,
uni-page-head,
.uni-page-head {
  background-color: var(--neutral-color-background) !important;
}

::v-deep .expressage {
  position: relative;
  .uni-data-checklist .checklist-group .checklist-box {
    margin-right: 0;
    .checkbox__inner {
      // background: url(@/static/checkbox.png) no-repeat !important;
      background-size: contain !important;
      border: 0 none !important;
    }
    &.is--default.is-checked .checkbox__inner {
      // background: url(@/static/checkboxActive.png) no-repeat !important;
      background-size: contain !important;
      .checkbox__inner-icon {
        opacity: 0;
      }
    }
  }
  .checkbox {
    position: absolute;
    left: 58rpx;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .cancelList {
    color: var(--neutral-color-font);
    .concelBtn {
      font-weight: 400;
    }
  }
}

.filtrateBox {
  display: flex;
  padding: 20rpx 34rpx;
  border-top: 1px solid var(--neutral-color-background);
  background: var(--neutral-color-white);
  ::v-deep .item {
    flex: 1;
    background: var(--neutral-color-background);
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 8rpx;
    text-align: center;
    color: var(--neutral-color-font);
    position: relative;
    padding-right: 46rpx;
    &:nth-child(2) {
      margin: 0 44rpx;
    }
    &:last-child {
      padding: 0 23rpx;
    }
    &.onHover {
      color: var(--essential-color-red);
    }
    icon {
      width: 20rpx;
      height: 12rpx;
      position: absolute;
      right: 26rpx;
    }
    .up {
      background: url(@/static/up.png) no-repeat;
      background-size: contain;
      top: 18rpx;
      &.hover {
        background: url(@/static/uphover.png) no-repeat;
        background-size: contain;
      }
    }
    .down {
      background: url(@/static/up.png) no-repeat;
      transform: rotate(180deg);
      background-size: contain;
      bottom: 18rpx;
      &.hover {
        background: url(@/static/uphover.png) no-repeat;
        transform: rotate(180deg);
        background-size: contain;
      }
    }
  }
}

::v-deep .adminActive {
  margin-left: 108rpx !important;
  margin-right: -16rpx !important;
}
::v-deep .turnBox {
  margin-top: 24rpx;
  .boxBg {
    margin-bottom: 32rpx;
  }
}
.concelBox {
  // margin-bottom: 32rpx;
  .tabConList {
    .item {
      display: flex;
      padding-left: 60rpx;
      padding-right: 50rpx;
      color: var(--neutral-color-main);
      & > view {
        display: block;
        flex: 1;
      }
      .cause {
        text-align: right;
        align-items: center;
        .nextIcon {
          margin-left: 20rpx;
          vertical-align: middle;
        }
      }
    }
  }
}
.conCenter {
  text-align: center;
}
// 多选框，选中-
:deep(
    .uni-data-checklist
      .checklist-group
      .checklist-box.is--default.is-checked
      .checkbox__inner
  ) {
  background-color: #f74145 !important;
  border-color: #f74145 !important;
  border-radius: 50% !important;
}
// 多选框
:deep(.uni-data-checklist .checklist-group .checklist-box .checkbox__inner) {
  border-radius: 50% !important;
}
// 多选框，右间距
:deep(
    .uni-data-checklist
      .checklist-group
      .checklist-box
      .checklist-content
      .checklist-text
  ) {
  margin-left: 6px !important;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 28rpx !important;
  color: #20232a !important;
  letter-spacing: 0.32rpx;
}
// 数字框
.uni-numbox {
  height: 68rpx;
  width: 224rpx;
  .uni-numbox__value {
    height: 68rpx;
    width: 92.6rpx;
    margin: 0;
    background-color: #fff !important;
    border: 1px solid #E5E4E4;
    box-sizing: border-box;
    border-left: none;
    border-right: none;
  }
  .uni-numbox-btns {
    width: 63rpx;
    padding: 0;
    background-color: #fff !important;
    border: 1px solid #E5E4E4;
    // .uni-numbox--text {
    //   color: transparent !important;
    // }
  }
  .uni-numbox__minus {
    border-top-left-radius: 50%;
    border-bottom-left-radius: 50%;
    position: relative;
    // &::before {
    //   content: '';
    //   width: 10px;
    //   height: 1.5px;
    //   position: absolute;
    //   left: 35%;
    //   background: #888888;
    //   border-radius: 0.62px;
    // }
  }
//   .uni-numbox--disabled {
//     color: transparent !important;
//     &::before {
//       content: '';
//       width: 10px;
//       height: 1.5px;
//       position: absolute;
//       left: 35%;
//       background: #e8e8e8;
//       border-radius: 0.62px;
//     }
// }
  .uni-numbox__plus {
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
    position: relative;
    // &::before {
    //   content: '';
    //   width: 10px;
    //   height: 1.5px;
    //   position: absolute;
    //   left: 35%;
    //   background: #888888;
    //   border-radius: 0.62px;
    // }
    // &::after {
    //   content: '';
    //   width: 10px;
    //   height: 1.5px;
    //   position: absolute;
    //   transform: rotate(90deg);
    //   left: 35%;
    //   top: 48%;
    //   background: #888888;
    //   border-radius: 0.62px;
    // }
}
}
.placeholderColor{
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 26rpx;
  color: #888;
}
