/**
 * 动态高度计算工具
 * 用于计算状态栏、导航栏、tab栏等高度，适配不同设备
 */
import { ref, computed } from 'vue';

export function useDynamicHeight() {
  // 系统信息相关
  const systemInfo = ref(null);
  const statusBarHeight = ref(0); // 状态栏高度
  const capsuleTop = ref(0); // 胶囊顶部距离
  const capsuleBottom = ref(0); // 胶囊底部距离
  const capsuleHeight = ref(0); // 胶囊高度

  /**
   * 像素转rpx的工具函数
   * @param {number} px 像素值
   * @returns {number} rpx值
   */
  const pxToRpx = (px) => {
    if (!systemInfo.value) return px * 2; // 默认转换比例
    // 根据设备屏幕宽度计算转换比例
    const ratio = 750 / systemInfo.value.screenWidth;
    return Math.round(px * ratio);
  };

  /**
   * 计算导航栏总高度（状态栏 + 胶囊高度 + 额外间距）
   */
  const navBarHeight = computed(() => {
    if (capsuleBottom.value && statusBarHeight.value) {
      // 将像素值转换为rpx + 额外间距（通常为8-12rpx）
      return pxToRpx(capsuleBottom.value) + 12;
    }
    return 166; // 默认值，单位rpx
  });

  /**
   * 计算tab栏的top位置（导航栏高度）
   */
  const tabBarTop = computed(() => {
    return navBarHeight.value;
  });

  /**
   * 计算内容区域的padding-top（导航栏高度 + tab栏高度）
   * @param {number} tabBarHeight tab栏高度，默认93rpx
   */
  const getContentPaddingTop = (tabBarHeight = 93) => {
    return computed(() => {
      return navBarHeight.value + tabBarHeight;
    });
  };

  /**
   * 初始化系统信息
   * 在页面onLoad生命周期中调用
   */
  const initSystemInfo = () => {
    return new Promise((resolve, reject) => {
      uni.getSystemInfo({
        success: (res) => {
          systemInfo.value = res;
          statusBarHeight.value = res.statusBarHeight;
          
          try {
            const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
            capsuleTop.value = menuButtonInfo.top;
            capsuleBottom.value = menuButtonInfo.bottom;
            capsuleHeight.value = menuButtonInfo.height;
            resolve({
              systemInfo: res,
              menuButtonInfo,
              navBarHeight: navBarHeight.value,
              tabBarTop: tabBarTop.value
            });
          } catch (error) {
            console.warn('获取胶囊按钮信息失败，使用默认值', error);
            resolve({
              systemInfo: res,
              menuButtonInfo: null,
              navBarHeight: navBarHeight.value,
              tabBarTop: tabBarTop.value
            });
          }
        },
        fail: (error) => {
          console.error('获取系统信息失败', error);
          reject(error);
        }
      });
    });
  };

  return {
    // 响应式数据
    systemInfo,
    statusBarHeight,
    capsuleTop,
    capsuleBottom,
    capsuleHeight,
    
    // 计算属性
    navBarHeight,
    tabBarTop,
    
    // 方法
    pxToRpx,
    getContentPaddingTop,
    initSystemInfo
  };
}

/**
 * 默认导出，提供常用的高度计算
 */
export default useDynamicHeight;
